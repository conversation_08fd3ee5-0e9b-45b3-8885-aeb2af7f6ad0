#pragma once
#include <cstddef>

namespace offsets
{
    constexpr std::ptrdiff_t dwCSGOInput = 0x192A5E0;
    constexpr std::ptrdiff_t dwEntityList = 0x18C1DE8;
    constexpr std::ptrdiff_t dwGameEntitySystem = 0x19DF790;
    constexpr std::ptrdiff_t dwGameEntitySystem_getHighestEntityIndex = 0x1510;
    constexpr std::ptrdiff_t dwGameRules = 0x191ECA0;
    constexpr std::ptrdiff_t dwGlobalVars = 0x1729BA0;
    constexpr std::ptrdiff_t dwGlowManager = 0x191F0C0;
    constexpr std::ptrdiff_t dwLocalPlayerController = 0x19115A8;
    constexpr std::ptrdiff_t dwLocalPlayerPawn = 0x17361E8;
    constexpr std::ptrdiff_t dwPlantedC4 = 0x1927A98;
    constexpr std::ptrdiff_t dwPrediction = 0x17360B0;
    constexpr std::ptrdiff_t dwSensitivity = 0x191F9E8;
    constexpr std::ptrdiff_t dwSensitivity_sensitivity = 0x40;
    constexpr std::ptrdiff_t dwViewAngles = 0x192F970;
    constexpr std::ptrdiff_t dwViewMatrix = 0x19231E0;
    constexpr std::ptrdiff_t dwViewRender = 0x19239E0;
}