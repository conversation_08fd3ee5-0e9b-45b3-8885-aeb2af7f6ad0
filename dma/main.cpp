#include "menu.h"

#include <d3d9.h>
#include <tchar.h>
#include <iostream>
#include <thread>

// cheat includes
#include "memory/cheat.hpp"
#include "memory/kmbox.hpp"
#include "memory/window/window.hpp"
#include "memory/config.hpp"

void aimbot( )
{
    while ( true )
    {
        updateAimbot->execute( );
    }
}

int main( )
{
    SetConsoleTitleA( "TOMO DMA PROJECT" );

    config = std::make_unique<Config>( );

    std::thread( window::create ).detach( );

    std::thread( aimbot ).detach( );
    
    cheat::init( );
}